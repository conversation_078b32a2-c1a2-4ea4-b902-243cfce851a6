{"name": "Claude Code Sandbox", "build": {"dockerfile": "Dockerfile", "args": {"TZ": "${localEnv:TZ:America/Los_Angeles}", "CLAUDE_CODE_VERSION": "latest", "GIT_DELTA_VERSION": "0.18.2", "ZSH_IN_DOCKER_VERSION": "1.2.0"}}, "runArgs": ["--cap-add=NET_ADMIN", "--cap-add=NET_RAW"], "customizations": {"vscode": {"extensions": ["dbaeumer.vscode-eslint", "esbenp.prettier-vscode", "eamodio.gitlens"], "settings": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "terminal.integrated.defaultProfile.linux": "zsh", "terminal.integrated.profiles.linux": {"bash": {"path": "bash", "icon": "terminal-bash"}, "zsh": {"path": "zsh"}}}}}, "remoteUser": "node", "mounts": ["source=claude-code-bashhistory-${devcontainerId},target=/commandhistory,type=volume", "source=claude-code-config-${devcontainerId},target=/home/<USER>/.claude,type=volume"], "containerEnv": {"NODE_OPTIONS": "--max-old-space-size=4096", "CLAUDE_CONFIG_DIR": "/home/<USER>/.claude", "POWERLEVEL9K_DISABLE_GITSTATUS": "true"}, "workspaceMount": "source=${localWorkspaceFolder},target=/workspace,type=bind,consistency=delegated", "workspaceFolder": "/workspace", "postCreateCommand": "sudo /usr/local/bin/init-firewall.sh"}