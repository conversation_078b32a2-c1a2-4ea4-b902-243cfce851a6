WITH project_branch_stats AS (
    SELECT
        p.id as projectId,
        pr."branchId",
        MAX(pr.revision) as max_revision,
        COUNT(pr.id) as revision_count,
        p."createdAt",
        p.name as project_name,
        w.name as workspace_name,
        t.name as team_name,
        t.id as team_id,
        array_agg(DISTINCT u.email) as user_emails,
        array_agg(DISTINCT u."firstName") as user_first_names
    FROM project_revision pr
    LEFT JOIN "user" u ON u.id = pr."createdById"
    LEFT JOIN project p ON p.id = pr."projectId"
    LEFT JOIN workspace w ON w.id = p."workspaceId"
    LEFT JOIN team t ON t.id = w."teamId"
    WHERE
        pr."createdAt" > '2025-04-23 03:10:00 +00:00'
        AND p."createdAt" < '2025-04-23 03:10:00 +00:00'
    GROUP BY p.id, pr."branchId", p."createdAt", p.name, w.name, t.name, t.id
),
latest_revisions AS (
    SELECT
        pr."projectId",
        pr."branchId",
        pr.revision,
        pr.data,
        pr."dataLength"
    FROM project_revision pr
    JOIN project_branch_stats pbs ON
        pr."projectId" = pbs.projectId AND
        pr."branchId" IS NOT DISTINCT FROM pbs."branchId" AND
        pr.revision = pbs.max_revision
),
earlier_revisions AS (
    SELECT
        pr."projectId",
        pr."branchId",
        pr.revision,
        pr.data,
        pr."dataLength"
    FROM project_revision pr
    JOIN project_branch_stats pbs ON
        pr."projectId" = pbs.projectId AND
        pr."branchId" IS NOT DISTINCT FROM pbs."branchId" AND
        pr.revision = (pbs.max_revision - pbs.revision_count)
)
SELECT
    pbs.projectId,
    pbs."branchId",
    pbs.max_revision as revision,
    pbs.revision_count as n,
    er."dataLength" before_length,
    lr."dataLength" after_length,
    pbs.user_emails,
    pbs.user_first_names,
    pbs."createdAt",
    pbs.project_name,
    pbs.workspace_name,
    pbs.team_name,
    pbs.team_id
FROM project_branch_stats pbs
LEFT JOIN latest_revisions lr ON
    lr."projectId" = pbs.projectId AND
    lr."branchId" IS NOT DISTINCT FROM pbs."branchId"
LEFT JOIN earlier_revisions er ON
    er."projectId" = pbs.projectId AND
    er."branchId" IS NOT DISTINCT FROM pbs."branchId"
WHERE lr.data != er.data and pbs.max_revision > 100
ORDER BY pbs.revision_count DESC
LIMIT 100;
