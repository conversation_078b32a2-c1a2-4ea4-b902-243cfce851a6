{"name": "plasmic-monorepo", "private": true, "scripts": {"prepare": "husky", "bootstrap": "bash scripts/bootstrap.sh", "bootstrap:platform": "yarn db:setup ; yarn setup-all ; yarn db:reset ; yarn setup-all", "bump": "echo 'Use the jenkins job release_public_packages instead: https://jenkins.aws.plasmic.app/job/release_public_packages/'", "claude": "echo 'Running tests before starting...' && if [ -f /.dockerenv ] && ! curl -s --max-time 5 https://example.com >/dev/null 2>&1; then claude --dangerously-skip-permissions --mcp-config=.claude/.mcp.json; else echo '<PERSON> may only be run in a Docker container with general network firewall, see .devcontainer/README.md'; exit 1; fi", "db:reset": "cd platform/wab && yarn db:reset", "db:setup": "cd platform/wab && yarn db:setup", "deps": "lerna list --include-dependencies --scope", "dev": "cd platform/wab && yarn dev", "dev:reset": "yarn setup && yarn db:reset && yarn dev", "format": "yarn prettier --write", "lint": "yarn eslint --fix", "e2e": "cd platform/wab && yarn cypress open", "e2e:headless": "cd platform/wab && yarn cypress run", "knip:deps": "NODE_OPTIONS=\"--max-old-space-size=10000\" knip --include dependencies", "local-canary": "yarn local-unpublish && lerna publish --canary --yes --include-merged-tags --no-git-tag-version --no-push --registry=http://localhost:4873 --force-publish", "local-publish": "./scripts/local-publish.sh", "local-unpublish": "lerna exec -- npm unpublish -f --registry=\"http://localhost:4873/\" \"\\${LERNA_PACKAGE_NAME}\"", "make": "cd platform/wab && make", "release": "echo 'Use the jenkins job release_public_packages instead: https://jenkins.aws.plasmic.app/job/release_public_packages/'", "setup": "yarn && yarn setup:wab && yarn setup:sub && yarn setup:react-web-bundle && yarn setup:live-frame && yarn setup:loader-bundle-env && yarn setup:loader-html-hydrate && yarn make", "setup-all": "yarn setup && yarn setup:canvas-packages && cd platform/wab && yarn typeorm migration:run && yarn migrate-dev-bundles && yarn plume:dev update", "setup:canvas-packages": "cd platform/canvas-packages && for d in internal_pkgs/*; do if [ -d \"$d\" ]; then cd $d && yarn && cd -; fi done && yarn && yarn build", "setup:live-frame": "cd platform/live-frame && yarn && yarn build", "setup:loader-bundle-env": "cd platform/loader-bundle-env && for d in internal_pkgs/*; do if [ -d \"$d\" ]; then cd $d && yarn && yarn build && cd -; fi done && yarn", "setup:loader-html-hydrate": "cd platform/loader-html-hydrate && yarn && yarn build && cp build/* ../wab/public/static/js/", "setup:react-web-bundle": "cd platform/react-web-bundle && yarn && yarn build", "setup:sub": "cd platform/sub && yarn && yarn build", "setup:wab": "cd platform/wab && yarn", "test": "jest $TEST_CWD", "typecheck": "cd platform/wab && yarn ts-watch", "upgrade-internal": "bash scripts/upgrade-internal.bash", "msw:update": "msw init ./msw --save=false"}, "resolutions": {"eslint": "^8.55.0", "jest": "29.7.0"}, "packageManager": "yarn@1.22.21", "devDependencies": {"@microsoft/api-extractor": "7.38.2", "@testing-library/react": "16.2.0", "@typescript-eslint/eslint-plugin": "6.21.0", "@typescript-eslint/parser": "6.21.0", "esbuild": "0.17.18", "esbuild-register": "^3.4.2", "eslint": "^8.55.0", "eslint-config-prettier": "9.1.0", "eslint-config-react-app": "7.0.1", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-no-relative-import-paths": "1.5.4", "eslint-plugin-prettier": "5.0.1", "eslint-plugin-react": "^7.33.2", "husky": "9.1.7", "if-env": "^1.0.4", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "knip": "5.45.0", "lint-staged": "16.1.4", "lerna": "^7.4.2", "msw": "2.7.3", "msw-storybook-addon": "2.0.4", "nx": "^16", "prettier": "2.8.8", "prettier-plugin-organize-imports": "^4.2.0", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.20.3", "typescript": "5.2.2", "storybook": "8.5.5", "storybook-addon-mock": "5.0.0", "@storybook/addon-essentials": "8.5.5", "@storybook/addon-interactions": "8.5.5", "@storybook/addon-links": "8.5.5", "@storybook/addon-webpack5-compiler-swc": "2.1.0", "@storybook/react": "8.5.5", "@storybook/react-webpack5": "8.5.5", "@storybook/test": "8.5.5", "@storybook/test-runner": "0.21.0", "@storybook/testing-library": "0.2.2"}, "workspaces": ["packages/auth-api", "packages/auth-react", "packages/cli", "packages/code-merger", "packages/create-plasmic-app", "packages/data-sources", "packages/data-sources-context", "packages/host", "packages/loader-core", "packages/loader-edge", "packages/loader-fetcher", "packages/loader-gatsby", "packages/loader-nextjs", "packages/loader-react", "packages/loader-splits", "packages/nextjs-app-router", "packages/prepass", "packages/query", "packages/react-web", "packages/react-web-runtime", "packages/watcher", "plasmicpkgs/airtable", "plasmicpkgs/tiptap", "plasmicpkgs/antd", "plasmicpkgs/antd5", "plasmicpkgs/chakra-ui", "plasmicpkgs/commerce-providers/*", "plasmicpkgs/fetch", "plasmicpkgs/framer-motion", "plasmicpkgs/graphql", "plasmicpkgs/keen-slider", "plasmicpkgs/lottie-react", "plasmicpkgs/plasmic-basic-components", "plasmicpkgs/plasmic-calendly", "plasmicpkgs/plasmic-cms", "plasmicpkgs/plasmic-content-stack", "plasmicpkgs/plasmic-contentful", "plasmicpkgs/plasmic-embed-css", "plasmicpkgs/plasmic-eventbrite", "plasmicpkgs/plasmic-giphy", "plasmicpkgs/plasmic-graphcms", "plasmicpkgs/plasmic-hubspot", "plasmicpkgs/plasmic-nav", "plasmicpkgs/plasmic-pigeon-maps", "plasmicpkgs/rive", "plasmicpkgs/plasmic-query", "plasmicpkgs/plasmic-rich-components", "plasmicpkgs/plasmic-sanity-io", "plasmicpkgs/plasmic-soundcloud", "plasmicpkgs/plasmic-strapi", "plasmicpkgs/plasmic-tabs", "plasmicpkgs/plasmic-typeform", "plasmicpkgs/plasmic-wordpress", "plasmicpkgs/plasmic-wordpress-graphql", "plasmicpkgs/plasmic-yotpo", "plasmicpkgs/radix-ui", "plasmicpkgs/react-aria", "plasmicpkgs/rive", "plasmicpkgs/react-audio-player", "plasmicpkgs/react-awesome-reveal", "plasmicpkgs/react-chartjs-2", "plasmicpkgs/plasmic-link-preview", "plasmicpkgs/react-parallax-tilt", "plasmicpkgs/react-quill", "plasmicpkgs/react-scroll-parallax", "plasmicpkgs/react-slick", "plasmicpkgs/react-twitter-widgets", "plasmicpkgs/react-youtube", "plasmicpkgs-dev"]}