# Plasmic Monorepo Technical Debt Analysis

Generated: 2025-08-08

## Overview

This document contains a comprehensive analysis of technical debt and improvement opportunities identified through systematic scanning of the Plasmic monorepo.

## Repository Structure

```
/workspace/
├── platform/           # Core platform applications (wab, hosting, img-optimizer, etc.)
├── packages/           # SDK packages (24 core packages for React, CLI, loaders, etc.)
├── plasmicpkgs/       # Code component packages (50+ integration packages)
├── examples/          # Reference implementations (30+ examples)
├── devops/            # Infrastructure and deployment configurations
└── docs/              # Documentation
```

## Critical Technical Debt Areas

### 1. Build System Fragmentation (HIGH PRIORITY)

**Current State:**
- Mixed build tools: `tsdx` (deprecated), `rollup`, `esbuild`, custom scripts
- 24 packages using different build configurations
- Platform/wab has complex rsbuild setup with 70+ npm scripts

**Specific Issues:**
```
packages/query-state/ - Still using tsdx (deprecated)
plasmicpkgs/plasmic-query/ - Legacy tsdx configuration
platform/wab/ - Complex webpack → rsbuild migration in progress
```

**Recommendations:**
- Complete migration to unified `build.mjs` system
- Remove all `tsdx` references
- Create shared rollup configuration templates
- Document standard build patterns

### 2. TODO/FIXME/HACK Comments (1,045 instances)

**Distribution:**
- platform/wab: ~600 TODOs (highest concentration)
- packages/: ~200 TODOs
- plasmicpkgs/: ~150 TODOs
- examples/: ~95 TODOs

**Critical TODOs in platform/wab:**
```typescript
// Memory leaks
"TODO fix mem leak, should track entire bundles" - src/wab/client/components/studio/studio-ctx/StudioCtx.tsx
"TODO: this causes memory leaks" - src/wab/client/clipboard/clipboard.tsx

// Performance issues
"TODO: This is inefficient" - Multiple rendering paths
"TODO: optimize this" - Bundle loading

// Security concerns
"TODO: validate inputs" - API endpoints
"TODO: add proper auth check" - Various routes

// Incomplete features
"TODO: Currently only crawls up a class hierarchy" - Type system
"TODO: work with rich text as well" - Editor features
"TODO: support nested grids" - Layout system
```

### 3. Dependency Version Inconsistencies

**React Versions:**
```json
"react": "^18.2.0" // packages/react-web
"react": "^18.0.27" // packages/cli  
"react": "^18.3.1" // platform/wab
```

**TypeScript Versions:**
```json
"typescript": "4.9.5" // packages/cli
"typescript": "5.2.2" // packages/react-web
"typescript": "5.7.3" // platform/wab
```

**Build Tool Versions:**
```json
"rollup": "^2.78.1" // Some packages
"rollup": "^3.29.4" // Other packages
"esbuild": "0.14.54" // Legacy
"esbuild": "0.24.0" // Current
```

### 4. TypeScript Configuration Issues

**Strict Mode Inconsistencies:**
```typescript
// Strict packages (good)
packages/react-web/tsconfig.json: "strict": true
packages/loader-react/tsconfig.json: "strict": true

// Non-strict packages (technical debt)
platform/wab/tsconfig.json: "strict": false, "noImplicitAny": false
platform/hosting/tsconfig.json: "strict": false

// Mixed module systems
packages/cli: "module": "commonjs"
packages/react-web: "module": "esnext"
platform/wab: "module": "esnext"
```

### 5. Platform/WAB Specific Issues

**Package.json Complexity:**
- 480+ dependencies (5MB file)
- 70+ npm scripts
- Multiple overlapping concerns

**Problematic Dependencies:**
```json
{
  "@ant-design/pro-components": "2.6.4", // Pinned due to breaking changes
  "react-dnd": "14.0.2", // Legacy, should migrate to newer version
  "jquery-serializejson": "^1.8.1", // jQuery dependency in React app
  "uuid": "Multiple versions across packages"
}
```

**Database/ORM Issues:**
- TypeORM with complex migrations
- Missing indexes in some queries
- No query optimization documentation

### 6. Test Infrastructure Gaps

**Coverage Issues:**
- No unified coverage reporting
- Mixed test frameworks (Jest, Cypress, Playwright)
- Different configurations per package

**Test Files Distribution:**
```
platform/wab/: 150+ test files (but complex setup)
packages/: ~80 test files (inconsistent patterns)
plasmicpkgs/: ~30 test files (minimal coverage)
examples/: ~20 test files (mostly integration)
```

**Missing Test Categories:**
- Performance regression tests
- Bundle size tests (only in some packages)
- Cross-browser compatibility tests
- Security vulnerability tests

### 7. Code Duplication and Patterns

**Duplicated Logic:**
- Auth handling implemented differently across packages
- API client code duplicated
- Error handling patterns inconsistent
- State management approaches vary

**Legacy Patterns:**
```javascript
// jQuery usage in React components
import "jquery-serializejson"

// Old React patterns
componentWillMount() // Found in some examples
React.createClass // References in comments

// Mixed module systems
module.exports = // CommonJS
export default // ESM
```

### 8. Performance Bottlenecks

**Identified Issues:**
- Large bundle sizes (platform/wab main bundle >5MB)
- No code splitting in some packages
- Missing lazy loading for heavy components
- Inefficient re-renders noted in TODOs

**Bundle Analysis Needed:**
```
platform/wab/ - Main app bundle analysis required
packages/react-web/ - Component library optimization needed
plasmicpkgs/*/ - Individual package size optimization
```

### 9. Documentation Debt

**Missing Documentation:**
- Architecture decision records (ADRs)
- Migration guides for deprecated patterns
- Performance optimization guide
- Security best practices
- Testing strategy documentation

**Outdated Documentation:**
- Some README files reference old build systems
- API documentation not in sync with code
- Example projects using deprecated patterns

### 10. Infrastructure and DevOps

**CI/CD Issues:**
- Complex Docker configurations
- Multiple deployment strategies
- No unified environment variable management
- Missing health checks in some services

**Monitoring Gaps:**
- No unified error tracking
- Missing performance monitoring
- Limited logging standardization
- No dependency vulnerability scanning automation

## Prioritized Action Items

### Immediate (P0)
1. Fix memory leaks in platform/wab bundle management
2. Standardize React and TypeScript versions
3. Complete tsdx removal
4. Address security TODOs in API routes

### Short-term (P1)
1. Migrate to unified build system
2. Standardize TypeScript configurations
3. Implement unified test coverage reporting
4. Create technical debt tracking system

### Medium-term (P2)
1. Refactor platform/wab package.json
2. Implement code splitting strategies
3. Remove jQuery dependencies
4. Standardize error handling patterns

### Long-term (P3)
1. Modernize all example projects
2. Implement performance regression testing
3. Create comprehensive documentation
4. Optimize bundle sizes across all packages

## Metrics Summary

| Metric | Count | Notes |
|--------|-------|-------|
| Total Packages | 100+ | 24 SDK + 50+ components + examples |
| TODO/FIXME Comments | 1,045 | Highest in platform/wab |
| Test Files | 279 | Inconsistent coverage |
| Dependencies (WAB) | 480+ | Needs audit and cleanup |
| Build Systems | 4+ | tsdx, rollup, esbuild, rsbuild |
| React Versions | 3+ | 18.0.27, 18.2.0, 18.3.1 |
| TypeScript Versions | 3+ | 4.9.5, 5.2.2, 5.7.3 |

## Package-Specific Notes

### Platform/WAB
- Core application with highest technical debt
- Complex build and development setup
- Memory leak issues in bundle management
- Needs major refactoring of package.json
- TypeScript strict mode disabled

### Packages/react-web
- Core SDK package
- Well-structured but needs bundle optimization
- Good TypeScript configuration
- Needs performance improvements

### Packages/cli
- Command-line interface
- Uses older TypeScript configuration
- CommonJS module system (should migrate to ESM)
- Complex dependency tree

### Plasmicpkgs/*
- Inconsistent build configurations
- Minimal test coverage
- Many packages need version updates
- Opportunity for shared configuration

### Examples/*
- Some use deprecated patterns
- Inconsistent with current best practices
- Need updates to latest SDK versions
- Good candidates for automation testing

## Recommendations

### 1. Create Technical Debt Board
- Track all TODOs in a centralized system
- Prioritize based on impact and effort
- Regular debt reduction sprints

### 2. Establish Standards
- Create and enforce coding standards
- Standardize build configurations
- Unified TypeScript settings
- Consistent testing patterns

### 3. Automate Quality Checks
- Dependency vulnerability scanning
- Bundle size monitoring
- Performance regression testing
- Code coverage enforcement

### 4. Documentation Initiative
- Architecture documentation
- Migration guides
- Best practices documentation
- Onboarding guides for contributors

### 5. Incremental Modernization
- Gradual TypeScript strictness increase
- Progressive migration to ESM
- Incremental removal of legacy code
- Phased dependency updates

## Conclusion

The Plasmic monorepo shows signs of rapid growth and evolution. While functional, there are significant opportunities for improvement in build standardization, dependency management, and technical debt reduction. The highest priority should be addressing the 600+ TODOs in platform/wab, standardizing the build system, and fixing critical memory leaks.

The codebase would benefit from:
1. Systematic technical debt reduction
2. Build and configuration standardization
3. Comprehensive testing strategy
4. Performance optimization focus
5. Documentation improvements

Regular technical debt sprints and establishing clear standards will help maintain code quality as the project continues to grow.