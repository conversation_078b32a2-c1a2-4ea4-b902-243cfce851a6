export interface ImgServerConfig {
  production: boolean;
  sentryDSN?: string;
  imgOptimizerHost: string;
  siteAssetsBucket: string;
  S3Endpoint?: string;
}

export const config: ImgServerConfig = {
  production: process.env.NODE_ENV === "production",
  sentryDSN: process.env.SENTRY_DSN,
  imgOptimizerHost: process.env.IMAGE_OPTIMIZER_HOST ?? "",
  siteAssetsBucket: process.env.SITE_ASSETS_BUCKET ?? "",
  S3Endpoint: process.env.S3_ENDPOINT,
};

console.info("ImgServerConfig", config);

if (!config.imgOptimizerHost || !config.siteAssetsBucket) {
  console.error(
    "🚨🚨🚨 ImgServerConfig missing environment variables. See .env.template."
  );
}
