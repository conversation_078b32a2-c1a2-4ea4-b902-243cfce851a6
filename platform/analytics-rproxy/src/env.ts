import { SaltManager } from "./salt";

export const PROCESS_ENV = {
  NATS_SERVERS: process.env.NATS_SERVERS ?? "nats://localhost:4222",
  POSTHOG_KEY: process.env.POSTHOG_KEY ?? "",
  POSTHOG_API_HOST: process.env.POSTHOG_API_HOST ?? "us.i.posthog.com",
  POSTHOG_ASSETS_HOST:
    process.env.POSTHOG_ASSETS_HOST ?? "us-assets.i.posthog.com",
};
console.log("PROCESS_ENV", PROCESS_ENV);
export type ProcessEnv = typeof PROCESS_ENV;

export interface Env {
  Variables: {
    processEnv: ProcessEnv;
    saltManager: SaltManager;
  };
}
