import { ENV } from "@/wab/client/env";
import { DefinePlugin } from "@rspack/core";

export const REQUIRED_VAR = Symbol();
export const OPTIONAL_VAR = Symbol();

/**
 * Converts real type to config type.
 *
 * Examples:
 *  `string` -> `string | typeof REQUIRED_VAR`
 *  `string | undefined` -> `string | typeof OPTIONAL_VAR`
 *  `string | null` -> `never` // for simplicity, null is not allowed
 */
type EnvVarValueConfig<T> = null extends T
  ? never
  : undefined extends T
  ? Exclude<T, undefined> | typeof OPTIONAL_VAR
  : T | typeof REQUIRED_VAR;
type EnvConfig = {
  [Key in keyof typeof ENV]: EnvVarValueConfig<typeof ENV[Key]>
};

/**
 * Type-safe way to configure DefinePlugin that matches ENV type.
 *
 * Each KEY will be available as `process.env.KEY` in client code.
 * The value can be:
 * - `FROM_PROCESS_ENV`: Uses the value from process.env at build-time, errors if not found
 * - `OPTIONAL_FROM_PROCESS_ENV`: Uses the value from process.env at build-time, never errors
 * - Any other value: Uses the provided value directly
 */
export class EnvPlugin extends DefinePlugin {
  constructor(envConfig: EnvConfig) {
    super(
      Object.fromEntries(
        Object.entries(envConfig).map(([key, value]) => {
          const envKey = `process.env.${key}`;

          // In EnvironmentPlugin, undefined = required, null = optional
          const processEnvValue = process.env[key];
          if (value === REQUIRED_VAR) {
            if (!processEnvValue) {
              throw new Error(`Env var ${key} missing`);
            }
            return [envKey, JSON.stringify(processEnvValue)];
          } else if (value === OPTIONAL_VAR) {
            return [
              envKey,
              processEnvValue ? JSON.stringify(processEnvValue) : undefined,
            ];
          } else {
            if (processEnvValue) {
              throw new Error(`Env var ${key} exists but not used`);
            }
            return [envKey, JSON.stringify(value)];
          }
        })
      )
    );
  }
}

type AssertTrue<T extends true> = T;
type AssertFalse<T extends false> = T;
type IsAssignable<AssignableTo, Type> = Type extends AssignableTo ? true : false;

type TestStringAssignableToString = AssertTrue<IsAssignable<EnvVarValueConfig<string>, string>>
type TestRequiredAssignableToString = AssertTrue<IsAssignable<EnvVarValueConfig<string>, typeof REQUIRED_VAR>>
type TestOptionalNotAssignableToString = AssertFalse<IsAssignable<EnvVarValueConfig<string>, typeof OPTIONAL_VAR>>
type TestStringAssignableToUndefined = AssertTrue<IsAssignable<EnvVarValueConfig<string | undefined>, string>>
type TestRequiredAssignableToUndefined = AssertFalse<IsAssignable<EnvVarValueConfig<string | undefined>, typeof REQUIRED_VAR>>
type TestOptionalNotAssignableToUndefined = AssertTrue<IsAssignable<EnvVarValueConfig<string | undefined>, typeof OPTIONAL_VAR>>
type TestStringAssignableToNull = AssertFalse<IsAssignable<EnvVarValueConfig<string | null>, string>>
type TestRequiredAssignableToNull = AssertFalse<IsAssignable<EnvVarValueConfig<string | null>, typeof REQUIRED_VAR>>
type TestOptionalNotAssignableToNull = AssertFalse<IsAssignable<EnvVarValueConfig<string | null>, typeof OPTIONAL_VAR>>
