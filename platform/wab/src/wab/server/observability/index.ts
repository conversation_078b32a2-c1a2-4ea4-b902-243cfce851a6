import { methodForwarder } from "@/wab/commons/methodForwarder";
import { initAmplitudeNode } from "@/wab/server/observability/amplitude-node";
import { initPosthogNode } from "@/wab/server/observability/posthog-node";
import { Analytics } from "@/wab/shared/observability/Analytics";
import { ConsoleLogAnalytics } from "@/wab/shared/observability/ConsoleLogAnalytics";

export function initAnalyticsFactory(opts: {
  production: boolean;
}): () => Analytics {
  const amplitudeApiKey = process.env.AMPLITUDE_API_KEY;
  const posthogApiKey = process.env.POSTHOG_API_KEY;
  const posthogApiHost = process.env.POSTHOG_API_HOST;

  if (!amplitudeApiKey || !posthogApiKey || !posthogApiHost) {
    console.warn(
      "Analytics environment variables not configured. Using ConsoleLogAnalytics."
    );
    return () => new ConsoleLogAnalytics();
  }

  const amplitudeFactory = initAmplitudeNode({
    apiKey: amplitudeApiKey,
  });
  const posthogFactory = initPosthogNode({
    apiKey: posthogApiKey,
    apiHost: posthogApiHost,
  });
  return () => {
    const amplitude = amplitudeFactory();
    const posthog = posthogFactory();
    return opts.production
      ? methodForwarder(amplitude, posthog)
      : methodForwarder(new ConsoleLogAnalytics(), amplitude, posthog);
  };
}
