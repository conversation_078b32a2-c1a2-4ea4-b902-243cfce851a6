/** @jest-environment node */
/*import { Request, Response } from "express-serve-static-core";
import { deleteSelf, getSelfOwnedTeams } from "./routes";
import { UserHasTeamOwnershipError } from "@/wab/shared/ApiErrors/cms-errors";

// Mock dependencies
jest.mock("@/wab/server/routes/util", () => ({
  userDbMgr: jest.fn(),
  getUser: jest.fn(),
}));

jest.mock("@/wab/server/routes/teams", () => ({
  mkApiTeam: jest.fn((team) => ({ ...team, type: "api" })),
}));

jest.mock("@/wab/server/auth/util", () => ({
  doLogout: jest.fn(),
}));

import { userDbMgr, getUser } from "@/wab/server/routes/util";
import { doLogout } from "@/wab/server/auth/util";

describe("deleteSelf", () => {
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockDbMgr: any;

  beforeEach(() => {
    mockReq = {
      session: {},
      sessionID: "test-session-id",
    };
    mockRes = {
      json: jest.fn(),
    };
    mockDbMgr = {
      checkUserIdIsSelf: jest.fn(),
      getUserById: jest.fn(),
      deleteUser: jest.fn(),
      getAffiliatedTeams: jest.fn(),
      getTeamOwners: jest.fn(),
    };
    
    (userDbMgr as jest.Mock).mockReturnValue(mockDbMgr);
    (getUser as jest.Mock).mockReturnValue({ id: "user-123" });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should delete user account when user has no team ownerships", async () => {
    const mockUser = { 
      id: "user-123", 
      email: "<EMAIL>",
      firstName: "Test",
      lastName: "User" 
    };
    
    mockDbMgr.checkUserIdIsSelf.mockReturnValue("user-123");
    mockDbMgr.getUserById.mockResolvedValue(mockUser);
    mockDbMgr.getAffiliatedTeams.mockResolvedValue([]);
    mockDbMgr.deleteUser.mockResolvedValue(undefined);
    (doLogout as jest.Mock).mockImplementation((req, res, callback?) => {
      if (callback) callback();
    });

    await deleteSelf(mockReq as Request, mockRes as Response);

    expect(mockDbMgr.checkUserIdIsSelf).toHaveBeenCalledWith("user-123");
    expect(mockDbMgr.getUserById).toHaveBeenCalledWith("user-123");
    expect(mockDbMgr.deleteUser).toHaveBeenCalledWith(mockUser, false);
    expect(doLogout).toHaveBeenCalledWith(mockReq, mockRes);
    expect(mockRes.json).toHaveBeenCalledWith({});
  });

  it("should throw error when user owns teams", async () => {
    const mockUser = { 
      id: "user-123", 
      email: "<EMAIL>",
      firstName: "Test",
      lastName: "User" 
    };
    const mockTeam = { 
      id: "team-123", 
      name: "Test Team" 
    };
    
    mockDbMgr.checkUserIdIsSelf.mockReturnValue("user-123");
    mockDbMgr.getUserById.mockResolvedValue(mockUser);
    mockDbMgr.getAffiliatedTeams.mockResolvedValue([mockTeam]);
    mockDbMgr.getTeamOwners.mockResolvedValue([mockUser]);

    await expect(deleteSelf(mockReq as Request, mockRes as Response))
      .rejects
      .toThrow(UserHasTeamOwnershipError);

    expect(mockDbMgr.deleteUser).not.toHaveBeenCalled();
    expect(doLogout).not.toHaveBeenCalled();
    expect(mockRes.json).not.toHaveBeenCalled();
  });

  it("should handle non-existent user gracefully", async () => {
    mockDbMgr.checkUserIdIsSelf.mockReturnValue("user-123");
    mockDbMgr.getUserById.mockResolvedValue(null);
    (doLogout as jest.Mock).mockImplementation((req, res, callback?) => {
      if (callback) callback();
    });

    await deleteSelf(mockReq as Request, mockRes as Response);

    expect(mockDbMgr.getUserById).toHaveBeenCalledWith("user-123");
    expect(mockDbMgr.deleteUser).not.toHaveBeenCalled();
    expect(doLogout).toHaveBeenCalledWith(mockReq, mockRes);
    expect(mockRes.json).toHaveBeenCalledWith({});
  });

  it("should delete user who is member (not owner) of teams", async () => {
    const mockUser = { 
      id: "user-123", 
      email: "<EMAIL>",
      firstName: "Test",
      lastName: "User" 
    };
    const mockTeam = { 
      id: "team-123", 
      name: "Test Team" 
    };
    const mockOwner = { 
      id: "owner-456", 
      email: "<EMAIL>" 
    };
    
    mockDbMgr.checkUserIdIsSelf.mockReturnValue("user-123");
    mockDbMgr.getUserById.mockResolvedValue(mockUser);
    mockDbMgr.getAffiliatedTeams.mockResolvedValue([mockTeam]);
    mockDbMgr.getTeamOwners.mockResolvedValue([mockOwner]); // Different user is owner
    mockDbMgr.deleteUser.mockResolvedValue(undefined);
    (doLogout as jest.Mock).mockImplementation((req, res, callback?) => {
      if (callback) callback();
    });

    await deleteSelf(mockReq as Request, mockRes as Response);

    expect(mockDbMgr.deleteUser).toHaveBeenCalledWith(mockUser, false);
    expect(doLogout).toHaveBeenCalled();
    expect(mockRes.json).toHaveBeenCalledWith({});
  });

  it("should throw error when user owns multiple teams", async () => {
    const mockUser = { 
      id: "user-123", 
      email: "<EMAIL>",
      firstName: "Test",
      lastName: "User" 
    };
    const mockTeam1 = { 
      id: "team-123", 
      name: "Test Team 1" 
    };
    const mockTeam2 = { 
      id: "team-456", 
      name: "Test Team 2" 
    };
    
    mockDbMgr.checkUserIdIsSelf.mockReturnValue("user-123");
    mockDbMgr.getUserById.mockResolvedValue(mockUser);
    mockDbMgr.getAffiliatedTeams.mockResolvedValue([mockTeam1, mockTeam2]);
    mockDbMgr.getTeamOwners
      .mockResolvedValueOnce([mockUser]) // Owner of team 1
      .mockResolvedValueOnce([mockUser]); // Owner of team 2

    await expect(deleteSelf(mockReq as Request, mockRes as Response))
      .rejects
      .toThrow(UserHasTeamOwnershipError);

    expect(mockDbMgr.deleteUser).not.toHaveBeenCalled();
    expect(doLogout).not.toHaveBeenCalled();
  });
});*/
