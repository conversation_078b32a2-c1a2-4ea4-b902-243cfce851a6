import { ENV } from "@/wab/client/env";
import { initAmplitudeBrowser } from "@/wab/client/observability/amplitude-browser";
import { initPosthogBrowser } from "@/wab/client/observability/posthog-browser";
import { initSentryBrowser } from "@/wab/client/observability/sentry-browser";
import { methodForwarder } from "@/wab/commons/methodForwarder";
import { ensure } from "@/wab/shared/common";
import type { Analytics } from "@/wab/shared/observability/Analytics";
import { ConsoleLogAnalytics } from "@/wab/shared/observability/ConsoleLogAnalytics";

let globalAnalytics: Analytics;

export function analytics(): Analytics {
  return globalAnalytics;
}

export function initObservability(): void {
  ensure(
    globalAnalytics === undefined,
    "Cannot initialize observability twice"
  );

  const production = ENV.NODE_ENV === "production";

  const amplitudeAnalytics = ENV.AMPLITUDE_API_KEY
    ? initAmplitudeBrowser({
        apiKey: ENV.AMPLITUDE_API_KEY,
      })
    : null;
  const posthogAnalytics =
    ENV.POSTHOG_API_KEY && ENV.POSTHOG_HOST
      ? initPosthogBrowser({
          apiKey: ENV.POSTHOG_API_KEY,
          host: ENV.POSTHOG_HOST,
          reverseProxyHost: ENV.POSTHOG_REVERSE_PROXY_HOST,
        })
      : null;
  const sentry =
    ENV.SENTRY_DSN &&
    ENV.SENTRY_ORG_ID &&
    ENV.SENTRY_PROJECT_ID &&
    posthogAnalytics
      ? initSentryBrowser({
          production,
          commitHash: ENV.COMMITHASH,
          dsn: ENV.SENTRY_DSN,
          orgId: ENV.SENTRY_ORG_ID,
          projId: +ENV.SENTRY_PROJECT_ID,
          posthogAnalytics,
        })
      : null;

  if (production) {
    globalAnalytics = methodForwarder(
      ensure(amplitudeAnalytics, "amplitude dep missing"),
      ensure(posthogAnalytics, "posthog dep missing")
    );
    ensure(sentry, "sentry dep missing");
  } else {
    globalAnalytics = methodForwarder(
      new ConsoleLogAnalytics(),
      amplitudeAnalytics,
      posthogAnalytics
    );
  }
  globalAnalytics.appendBaseEventProperties({
    production,
    commitHash: ENV.COMMITHASH,
  });
}

export const _testonly = {
  setGlobalAnalytics: (testAnalytics: Analytics) => {
    globalAnalytics = testAnalytics;
  },
};
