{
  "compilerOptions": {
    "target": "es2017",
    "module": "esnext",
    "lib": [
      "dom",
      "dom.iterable",
      "es2016",
      "es2017",
      "es2018",
      "es2019",
      "es2020",
      "esnext",
      "esnext.asynciterable",
      "esnext.array"
    ],
    // .js were causing problems.  Getting "Cannot compile namespaces" errors
    // ([1]), and `String.format` in devtools.js was causing weird type
    // inference issues with strings (especially causing JQuery.data() to
    // break).
    //
    // [1]: https://github.com/Microsoft/TypeScript/issues/15230#issuecomment-466624686
    "allowJs": false,
    "allowSyntheticDefaultImports": true,
    "alwaysStrict": true,
    "baseUrl": "./",
    "paths": {
      // Absolute import paths like "@/wab/shared/common"
      // Should match "resolve.alias" in webpack.config.js
      "@/*": ["./src/*"]
    },
    "esModuleInterop": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true,
    "jsx": "react",
    "moduleResolution": "node",
    "noEmit": true,
    "noErrorTruncation": true,
    "noFallthroughCasesInSwitch": true,
    // Not worth it
    "noImplicitAny": false,
    // Not worth it
    "noImplicitOverride": false,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    // Not worth it
    "noPropertyAccessFromIndexSignature": false,
    "resolveJsonModule": true,
    "skipLibCheck": true,
    "strict": false,
    "strictFunctionTypes": true,
    "strictNullChecks": true
  },
  "include": ["src", "tools/webpack", "rsbuild.config.ts"],
  "exclude": [
    "node_modules",
    "build",
    "tools",
    "acceptance-tests",
    "webpack",
    "jest",
    "src/wab/client/test/setupTests.ts",
    "__tests__"
  ]
}
