# To test:
# git clone --bare **************:plasmicapp/plasmic ~/proj/tmp/publicrepobare
# docker run -it -v ~/.git-credentials:/root/.git-credentials -v "$(pwd):/usr/src/app"  -v ~/.ssh/id_rsa:/root/.ssh/id_rsa -v /tmp/.ssh/config:/root/.ssh/config -v ~/.ssh/id_rsa.pub:/root/.ssh/id_rsa.pub -v ~/.gitconfig:/root/.gitconfig -v ~/proj/plasmic:/private -v ~/proj/tmp/publicrepobare:/public -e COPYBARA_OPTIONS=--force -e COPYBARA_WORKFLOW=private_to_public -e COPYBARA_SOURCEREF= yaaang/copybara copybara

# Must use https for gerrit API calls to work (to create CRs).
# The username and password for this should be stored in git-credentials.
sourceUrl = "ssh://**************/plasmicapp/plasmic-internal.git"
destinationUrl = "ssh://**************/plasmicapp/plasmic.git"

# Use existing local repo, to avoid a slow fresh clone on each run
privateLocalUrl = "file:///private"
publicLocalUrl = "file:///public"

patterns_for_public = '''
docs/**
examples/**
packages/**
plasmicpkgs/**
plasmicpkgs-dev/**
platform/canvas-packages/**
platform/integration-tests/**
platform/host-test/**
platform/live-frame/**
platform/loader-bundle-env/**
platform/loader-html-hydrate/**
platform/loader-tests/**
platform/react-renderer/**
platform/react-web-bundle/**
platform/pybackend/**
platform/sub/**
platform/wab/**
scripts/**
*.*
.github/workflows/cla.yaml
.eslintignore
.eslintrc.js
.eslintrc.react.js
.gitignore
.gitreview
.nxignore
.pre-commit-config.yaml
.prettierignore
.prettierrc
.regula.yaml
.tool-versions
'''.strip().split('\n')

exclude_patterns = '''
**/.delivery/**
**/internal/**
**/*.internal*
copy.bara.sky
'''.strip().split('\n')

globs_for_public = \
    glob(
        patterns_for_public,
        exclude = exclude_patterns)

def overlay_external_files(ctx):
    for f in ctx.run(glob(["**/*.external*"])):
        ctx.run(core.move(f.path, f.path.replace(".external",""), overwrite=True))

core.workflow(
    name = "private_to_public_push",
    origin = git.origin(
        url = sourceUrl,
        ref = 'master',
    ),
    destination = git.github_destination(
        url = destinationUrl,
        fetch = 'master',
        push = 'master',
    ),
    origin_files = globs_for_public,
    destination_files = glob(["**"]),
    authoring = authoring.pass_thru("Plasmic Repo Bot <<EMAIL>>"),
    mode = "ITERATIVE",
    transformations = [
        overlay_external_files,
    ]
)
