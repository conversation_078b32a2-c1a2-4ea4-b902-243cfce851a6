{"name": "@plasmicpkgs/plasmic-basic-components", "version": "0.0.251", "description": "Plasmic registration call for the HTML5 video element", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/plasmic-basic-components.esm.js", "files": ["dist"], "size-limit": [{"path": "dist/plasmic-basic-components.cjs.production.min.js", "limit": "10 KB"}, {"path": "dist/plasmic-basic-components.esm.js", "limit": "10 KB"}], "scripts": {"build": "tsdx build", "start": "tsdx watch", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test --passWithNoTests", "lint": "tsdx lint", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why", "storybook": "storybook dev -p 6006 --no-open", "build-storybook": "storybook build", "test-storybook": "test-storybook"}, "devDependencies": {"@plasmicapp/host": "1.0.224", "@plasmicapp/query": "0.1.80", "@size-limit/preset-small-lib": "^4.11.0", "@types/node": "^14.0.26", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "react": "18.2.0", "react-dom": "18.2.0", "size-limit": "^4.11.0", "tsdx": "^0.14.1", "tslib": "^2.2.0"}, "peerDependencies": {"@plasmicapp/host": ">=1.0.0", "@plasmicapp/query": "^0.1.64", "react": ">=16.8.0", "react-dom": ">=16.8.0"}}