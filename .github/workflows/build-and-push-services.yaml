name: Platform CD GCP

on:
  push:
    branches:
      - master
    paths:
      - "platform/analytics-rproxy/**"
      - "platform/wab/**"
      - "platform/loader-html-hydrate/**"
      - "platform/loader-bundle-env/**"
      - "platform/img-optimizer/**"
      - "platform/sub/**"
      - "platform/react-web-bundle/**"
      - "platform/live-frame/**"
      - "platform/canvas-packages/**"
  workflow_dispatch:
    inputs:
      environment:
        description: Environment to deploy (e.g. prod, dev)
        required: true
        default: dev
        type: choice
        options:
          - dev
          - prod
      action:
        description: |
          The intetion is to rollback or to deploy?
          Rollback needs a 6 digit commit sha to rollback to.
          Deploy will trigger a new build.

          NOTE: For now rollback is only supported for prod
        required: true
        default: deploy
        type: choice
        options:
          - rollback
          - deploy
      commit-sha:
        description: a 6 digit commit sha
        required: false
        type: string
      component:
        description: when rolling back need to specify a component to rollback
        required: false
        type: choice
        options:
          - codegen
          - img_optimizer
          - analytics-rproxy

permissions:
  contents: write
  id-token: write
  pull-requests: read

concurrency:
  group: ${{ github.ref }}
  cancel-in-progress: true

jobs:
  setup-tags:
    runs-on: ubuntu-latest
    outputs:
      # prod
      tag_prod: ${{ steps.set-vars.outputs.tag_prod }}
      gcs_bucket_prod: ${{ steps.set-vars.outputs.gcs_bucket_prod }}
      gcp_project_prod: ${{ steps.set-vars.outputs.gcp_project_prod }}
      # dev
      tag_dev: ${{ steps.set-vars.outputs.tag_dev }}
      gcs_bucket_dev: ${{ steps.set-vars.outputs.gcs_bucket_dev }}
      gcp_project_dev: ${{ steps.set-vars.outputs.gcp_project_dev }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: When workflow_dispatch targets prod fail if not on master branch
        if: github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'prod'
        run: |
          echo "Running on branch: ${{ github.ref }}"
          if [[ "${{ github.ref }}" != "refs/heads/master" ]]; then
            echo "❌ This workflow must be run from the master branch when deploying to production."
            exit 1
          fi

      - name: When rollback runs for dev env fail
        if: github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'dev' && github.event.inputs.action == 'rollback'
        run: |
          echo "❌ rollback action is only supported in production."
          exit 1

      - name: When rollback needs a commit sha
        if: github.event_name == 'workflow_dispatch' && github.event.inputs.action == 'rollback' && github.event.inputs.commit-sha == ''
        run: |
          echo "❌ rollback action needs a commit-sha."
          exit 1

      - name: When rollback needs to specify a component
        if: github.event_name == 'workflow_dispatch' && github.event.inputs.action == 'rollback' && github.event.inputs.component == ''
        run: |
          echo "❌ rollback action needs to specify a component."
          exit 1

      - name: Determine env vars
        id: set-vars
        run: |
          COMMIT_HASH=$(git rev-parse --short=6 HEAD)
          RUN_NUMBER="${{ github.run_number }}"

          echo tag_dev=dev-${RUN_NUMBER}-${COMMIT_HASH} >> "$GITHUB_OUTPUT"
          echo gcs_bucket_dev=studio-dev-plasmic-app >> "$GITHUB_OUTPUT"
          echo gcp_project_dev=platform-dev-442916 >> "$GITHUB_OUTPUT"

          echo tag_prod=${RUN_NUMBER}-$COMMIT_HASH >> "$GITHUB_OUTPUT"
          echo gcs_bucket_prod=studio-plasmic-app >> "$GITHUB_OUTPUT"
          echo gcp_project_prod=platform-442916 >> "$GITHUB_OUTPUT"

  changes:
    if: github.event_name == 'push' || (github.event_name == 'workflow_dispatch' && github.event.inputs.action == 'deploy')
    runs-on: ubuntu-latest
    outputs:
      services: ${{ steps.filter.outputs.changes }}
      studio_cd: ${{ steps.filter.outputs.studio_cd }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 100
          fetch-tags: true

      - name: Determine base for comparison
        id: set-base
        run: |
          BASE=master
          if [ "${{ github.event_name }}" == "workflow_dispatch" ] && [ "${{ github.event.inputs.environment }}" == "prod" ]; then
            BASE=latest-released
          fi
          echo "BASE=$BASE" >> "$GITHUB_OUTPUT"

      - name: Filter files changed
        uses: dorny/paths-filter@v3
        id: filter
        with:
          base: ${{ steps.set-base.outputs.BASE }}
          initial-fetch-depth: 100
          filters: |
            codegen:
              - "platform/wab/**"
              - "platform/loader-html-hydrate/**"
              - "platform/loader-bundle-env/**"
            img_optimizer:
              - "platform/img-optimizer/**"
            studio_cd:
              - "platform/wab/**"
              - "platform/loader-html-hydrate/**"
              - "platform/loader-bundle-env/**"
              - "platform/sub/**"
              - "platform/react-web-bundle/**"
              - "platform/live-frame/**"
              - "platform/canvas-packages/**"
            analytics-rproxy:
              - "platform/analytics-rproxy/**"

  # if changes skip because the action is a rollback this will skip as well.
  services-build:
    runs-on: arc-runner-set-4cpus-16gb
    needs: [changes, setup-tags]
    if: ${{ needs.changes.outputs.services != '[]' && needs.changes.outputs.services != '["studio_cd"]' }} # Only run if there are services other than studio_cd
    strategy:
      matrix:
        # we use matrix here to be able to run multiple build of services with repeating code
        service: ${{ fromJSON(needs.changes.outputs.services) }}
        exclude:
          - service: "studio_cd"
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Authenticate with GCP
        id: auth
        uses: "google-github-actions/auth@v2"
        with:
          token_format: access_token
          service_account: <EMAIL>
          project_id: "registries-442916"
          workload_identity_provider: "projects/************/locations/global/workloadIdentityPools/github-actions/providers/github-actions"

      - name: Login to GCR
        uses: docker/login-action@v3
        with:
          registry: us-central1-docker.pkg.dev
          username: oauth2accesstoken
          password: ${{ steps.auth.outputs.access_token }}

      # this way we can set context and dockerfile path based on matrix, otherwise we will end up running
      # both contexts for both Dockerfiles: https://github.com/plasmicapp/plasmic-internal/actions/runs/***********
      # TODO: Will be nice to find a better way of doing this without needing bash scripting
      - name: Set context and Dockerfile path dynamically
        id: set-context
        run: |
          if [ "${{ matrix.service }}" == "codegen" ]; then
            echo "context=platform/" >> $GITHUB_ENV
            echo "dockerfile=platform/wab/Dockerfile" >> $GITHUB_ENV  # Set the Dockerfile path for codegen
          elif [ "${{ matrix.service }}" == "img_optimizer" ]; then
            echo "context=platform/img-optimizer" >> $GITHUB_ENV
            echo "dockerfile=platform/img-optimizer/Dockerfile" >> $GITHUB_ENV  # Set the Dockerfile path for img_optimizer
          elif [ "${{ matrix.service }}" == "analytics-rproxy" ]; then
            echo "context=platform" >> $GITHUB_ENV
            echo "dockerfile=platform/analytics-rproxy/Dockerfile" >> $GITHUB_ENV
          fi

          if [ "${{ github.ref }}" == "refs/heads/master" ]; then
            echo "TAGS<<EOF" >> $GITHUB_ENV
            echo "us-central1-docker.pkg.dev/registries-442916/plasmic-prod-us-central1/${{ matrix.service }}:${{ needs.setup-tags.outputs.tag_prod }}" >> $GITHUB_ENV
            echo "us-central1-docker.pkg.dev/registries-442916/plasmic-prod-us-central1/${{ matrix.service }}:${{ needs.setup-tags.outputs.tag_dev }}"  >> $GITHUB_ENV
            echo "us-central1-docker.pkg.dev/registries-442916/plasmic-prod-us-central1/${{ matrix.service }}:latest"                                   >> $GITHUB_ENV
            echo "EOF"       >> $GITHUB_ENV
          else
            echo "TAGS<<EOF" >> $GITHUB_ENV
            echo "us-central1-docker.pkg.dev/registries-442916/plasmic-prod-us-central1/${{ matrix.service }}:${{ needs.setup-tags.outputs.tag_dev }}" >> $GITHUB_ENV
            echo "EOF"       >> $GITHUB_ENV
          fi

      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          context: ${{ env.context }}
          file: ${{ env.dockerfile }}
          push: true
          tags: ${{ env.TAGS }}
          platforms: linux/amd64
          # to improve build time, we're using caching for both services. We pushed only Docker layers first
          # and in the next run it'll pull the cached layers
          cache-from: type=registry,ref=us-central1-docker.pkg.dev/registries-442916/plasmic-prod-us-central1/${{ matrix.service }}:cached
          cache-to: type=registry,ref=us-central1-docker.pkg.dev/registries-442916/plasmic-prod-us-central1/${{ matrix.service }}:cached,mode=max

  # if changes skip because the action is a rollback this will skip as well
  studio-cd:
    needs: [changes, setup-tags]
    if: ${{ needs.changes.outputs.studio_cd == 'true' }}
    # TODO: add Sentry release step
    runs-on: arc-runner-set-4cpus-16gb
    env:
      CI: "true"
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: 24.4.0

      - name: Install yarn
        run: npm i -g yarn

      - name: Install Root dependencies
        uses: nick-fields/retry@v3
        with:
          command: yarn
          max_attempts: 3
          timeout_minutes: 10

      - name: Install wab dependencies
        uses: nick-fields/retry@v3
        with:
          command: |
            cd platform/wab
            yarn
          max_attempts: 3
          timeout_minutes: 10

      - name: Build sub
        uses: nick-fields/retry@v3
        with:
          command: |
            cd platform/sub
            yarn
            yarn build
          max_attempts: 3
          timeout_minutes: 10

      - name: Build react-web-bundle
        uses: nick-fields/retry@v3
        with:
          command: |
            cd platform/react-web-bundle
            yarn
            yarn build
          max_attempts: 3
          timeout_minutes: 10

      - name: Build live-frame
        uses: nick-fields/retry@v3
        with:
          command: |
            cd platform/live-frame
            yarn
            yarn build
          max_attempts: 3
          timeout_minutes: 10

      - name: Build canvas-packages
        uses: nick-fields/retry@v3
        with:
          command: |
            cd platform/canvas-packages

            for d in internal_pkgs/*; do
              if [ -d "$d" ]; then
                pushd $d
                yarn
                popd
              fi
            done

            yarn
            yarn build
          max_attempts: 3
          timeout_minutes: 10

      - name: Build loader-html-hydrate
        uses: nick-fields/retry@v3
        with:
          command: |
            cd platform/loader-html-hydrate
            yarn
            yarn build
            cp -rp ./build/* ../wab/public/static/js/
          max_attempts: 3
          timeout_minutes: 10

      - name: Build wab
        uses: nick-fields/retry@v3
        env:
          PUBLIC_URL: https://studio.dev.plasmic.app
          AMPLITUDE_API_KEY: "1efde847a1dd16e6dbf8a242c1e2dd07"
          INTERCOM_APP_ID: "wy0ngfce"
          POSTHOG_API_KEY: "phc_eaI1hFsPRIZkmwrXaSGRNDh4H9J3xdh1j9rgNy27NgP"
          POSTHOG_HOST: "https://us.i.posthog.com"
          POSTHOG_REVERSE_PROXY_HOST: "https://a.plasmic.app"
          SENTRY_ORG_ID: "plasmicapp"
          SENTRY_PROJECT_ID: "1840236"
          SENTRY_DSN: "https://<EMAIL>/1840236"
          STRIPE_PUBLIC_KEY: "pk_live_s4L5mrGSii9v1JNt1CPrXPxv00yncZqGGV"
        with:
          command: |
            cd platform/wab
            make
            yarn build-css
            NO_TYPECHECK=1 NODE_OPTIONS="--max_old_space_size=12288" yarn build
          max_attempts: 3
          timeout_minutes: 10

      - name: Authenticate with GCP
        id: auth
        uses: "google-github-actions/auth@v2"
        with:
          token_format: access_token
          service_account: <EMAIL>
          project_id: registries-442916
          workload_identity_provider: projects/************/locations/global/workloadIdentityPools/github-actions/providers/github-actions

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          version: ">= 363.0.0"

      - name: Push version
        working-directory: platform/wab/build
        run: |
          if [ "${{ github.ref }}" == "refs/heads/master" ]; then
            gcloud storage cp -r --quiet ./ gs://${{ needs.setup-tags.outputs.gcs_bucket_prod }}/versions/${{ needs.setup-tags.outputs.tag_prod }}/
            gcloud storage cp -r --quiet ./ gs://${{ needs.setup-tags.outputs.gcs_bucket_dev }}/versions/${{ needs.setup-tags.outputs.tag_dev }}/
          else
            gcloud storage cp -r --quiet ./ gs://${{ needs.setup-tags.outputs.gcs_bucket_dev }}/versions/${{ needs.setup-tags.outputs.tag_dev }}/
          fi

      - name: Deploy
        working-directory: platform/wab/build
        run: |
          function deploy() {
            local bucket="$1"
            local project="$2"

            gcloud storage cp -r --quiet ./ gs://${bucket}/
            gcloud storage cp -r --quiet ./index.html gs://${bucket}/index.html --cache-control 'max-age=0, s-maxage=31536000'
            gcloud storage cp -r --quiet ./static/js/studio.js gs://${bucket}/static/js/studio.js --cache-control 'max-age=0, s-maxage=31536000'
            gcloud compute url-maps invalidate-cdn-cache platform-url-map --path "/index.html" --async --project ${project}
            gcloud compute url-maps invalidate-cdn-cache platform-url-map --path "/static/js/studio.js" --async --project ${project}

          }
          if [ "${{ github.ref }}" == "refs/heads/master" ]; then
            deploy ${{ needs.setup-tags.outputs.gcs_bucket_prod }} ${{ needs.setup-tags.outputs.gcp_project_prod }}
            deploy ${{ needs.setup-tags.outputs.gcs_bucket_dev }} ${{ needs.setup-tags.outputs.gcp_project_dev }}
          else
            deploy ${{ needs.setup-tags.outputs.gcs_bucket_dev }} ${{ needs.setup-tags.outputs.gcp_project_dev }}
          fi

  update-latest-tag:
    runs-on: ubuntu-latest
    needs: [services-build, studio-cd] # or all jobs that must succeed before this
    if: >
      github.ref == 'refs/heads/master'
    steps:
      - name: Get Github App Token
        uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.PLASMIC_WORKFLOW_APP_ID }}
          private-key: ${{ secrets.PLASMIC_WORKFLOW_SECRET }}

      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Required to tag correctly
          fetch-tags: true
          token: ${{ steps.app-token.outputs.token }}

      - name: Set up Git config
        run: |
          git config user.name "github-actions"
          git config user.email "<EMAIL>"

      - name: Create or update latest-released tag
        run: |
          git tag -f latest-released
          git push origin -f latest-released

  rollback:
    runs-on: ubuntu-latest
    needs: ["setup-tags"]
    if: github.event_name == 'workflow_dispatch' && github.event.inputs.action == 'rollback'
    steps:
      - name: Authenticate with GCP
        id: auth
        uses: "google-github-actions/auth@v2"
        with:
          token_format: access_token
          service_account: <EMAIL>
          project_id: "registries-442916"
          workload_identity_provider: "projects/************/locations/global/workloadIdentityPools/github-actions/providers/github-actions"

      - name: Login to GCR
        uses: docker/login-action@v3
        with:
          registry: us-central1-docker.pkg.dev
          username: oauth2accesstoken
          password: ${{ steps.auth.outputs.access_token }}

      - uses: imjasonh/setup-crane@v0.1

      - name: Find tag
        id: find-tag
        run: |
          TAG=$(crane ls us-central1-docker.pkg.dev/registries-442916/plasmic-prod-us-central1/${{ github.event.inputs.component }} \
          | grep "${{ github.event.inputs.commit-sha }}\$" \
          | grep -v "^dev-" \
          | sort \
          | tail -n1)

          if [ -z "$TAG" ]; then
            echo "❌ No tag found ending with ${{ github.event.inputs.commit-sha }}"
            exit 1
          fi

          echo "✅ Found existing tag: $TAG"
          echo "old_tag=$TAG" >> "$GITHUB_OUTPUT"
          echo "new_tag=${{ github.run_number }}-${{ github.event.inputs.commit-sha }}" >> "$GITHUB_OUTPUT"

      - name: Retag Image
        run: |
          OLD_TAG=${{ steps.find-tag.outputs.old_tag }}
          NEW_TAG=${{ steps.find-tag.outputs.new_tag }}

          crane tag \
            us-central1-docker.pkg.dev/registries-442916/plasmic-prod-us-central1/${{ github.event.inputs.component }}:$OLD_TAG $NEW_TAG

      - name: Set up kubectl
        if: github.event.inputs.component == 'codegen'
        uses: azure/setup-kubectl@v3

      - name: Get GKE Creds
        if: github.event.inputs.component == 'codegen'
        uses: google-github-actions/get-gke-credentials@v2
        with:
          cluster_name: platform-us-central1
          location: us-central1
          project_id: platform-442916

      - name: Setup Flux CLI
        if: github.event.inputs.component == 'codegen'
        uses: fluxcd/flux2/action@main

      - name: Wait for Codegen Rollout
        if: github.event.inputs.component == 'codegen'
        run: |
          flux reconcile image repository codegen
          flux reconcile image update flux-system
          sleep 1m # wait a min to let flux push the tag bump and rollout to initiate
          kubectl rollout status deploy/app -n platform --timeout=10m
          kubectl rollout status deploy/codegen-heavy-ops -n platform --timeout=10m
          kubectl rollout status deploy/codegen-light-ops -n platform --timeout=10m

      - name: Rollback Sudio SPA
        if: github.event.inputs.component == 'codegen'
        run: |
          gcloud storage cp -r --quiet \
            gs://${{ needs.setup-tags.outputs.gcs_bucket_prod }}/versions/${{ steps.find-tag.outputs.old_tag }}/* \
            gs://${{ needs.setup-tags.outputs.gcs_bucket_prod }}/versions/${{ steps.find-tag.outputs.new_tag }}/

          gcloud storage cp -r --quiet \
            gs://${{ needs.setup-tags.outputs.gcs_bucket_prod }}/versions/${{ steps.find-tag.outputs.old_tag }}/* \
            gs://${{ needs.setup-tags.outputs.gcs_bucket_prod }}/

          gcloud storage cp --quiet \
            --cache-control='max-age=0, s-maxage=31536000' \
            gs://${{ needs.setup-tags.outputs.gcs_bucket_prod }}/versions/${{ steps.find-tag.outputs.old_tag }}/index.html \
            gs://${{ needs.setup-tags.outputs.gcs_bucket_prod }}/index.html

          gcloud storage cp --quiet \
            --cache-control='max-age=0, s-maxage=31536000' \
            gs://${{ needs.setup-tags.outputs.gcs_bucket_prod }}/versions/${{ steps.find-tag.outputs.old_tag }}/static/js/studio.js \
            gs://${{ needs.setup-tags.outputs.gcs_bucket_prod }}/static/js/studio.js

          gcloud compute url-maps invalidate-cdn-cache platform-url-map \
            --path "/index.html" --async --project ${{ needs.setup-tags.outputs.gcp_project_prod }}

          gcloud compute url-maps invalidate-cdn-cache platform-url-map \
            --path "/static/js/studio.js" --async --project ${{ needs.setup-tags.outputs.gcp_project_prod }}
